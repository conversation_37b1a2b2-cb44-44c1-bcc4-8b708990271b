/* Enhanced Encryption Application Styles */
/* Modern, responsive design with dark/light theme support */

:root {
    /* Dark theme colors */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #666666;
    --accent-primary: #6366f1;
    --accent-secondary: #8b5cf6;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --border: #333333;
    --border-focus: #6366f1;
    --shadow: rgba(0, 0, 0, 0.3);
    --gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

[data-theme="light"] {
    /* Light theme colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --accent-primary: #6366f1;
    --accent-secondary: #8b5cf6;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --border: #e2e8f0;
    --border-focus: #6366f1;
    --shadow: rgba(0, 0, 0, 0.1);
    --gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: var(--border) var(--bg-secondary);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    transition: all 0.3s ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.header-content {
    position: relative;
    padding: 2rem 0;
}

h1 {
    font-size: 3rem;
    font-weight: 700;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    text-shadow: 0 4px 8px var(--shadow);
}

.subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 400;
    margin-bottom: 1rem;
}

.theme-toggle {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--bg-tertiary);
    border: 2px solid var(--border);
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    transform: scale(1.1);
    border-color: var(--accent-primary);
    box-shadow: 0 4px 12px var(--shadow);
}

/* Main Content */
main {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
    gap: 1.5rem;
    align-items: start;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    align-items: stretch;
    min-height: 400px;
}

.third-row {
    display: flex;
    width: 100%;
}

.third-row .third-column {
    flex: 1;
}

/* Controls Section */
.controls-section {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 1rem;
    border: 1px solid var(--border);
    box-shadow: 0 4px 12px var(--shadow);
    height: fit-content;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

select, input[type="number"], input[type="text"] {
    background: var(--bg-tertiary);
    border: 2px solid var(--border);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

select:focus, input:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.compact-select {
    max-width: 250px;
    margin-bottom: 0;
}

/* Algorithm and Settings Row */
.algorithm-settings-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 1rem;
    margin-bottom: 1rem;
}

.algorithm-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Settings Panel */
.setting-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

/* Compact Info Panel */
.info-panel-compact {
    background: var(--bg-tertiary);
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    margin-top: 1rem;
}

.info-panel-compact h4 {
    color: var(--accent-primary);
    margin-bottom: 0.5rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-secondary);
}

.toggle-label input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    width: 3rem;
    height: 1.5rem;
    background: var(--bg-tertiary);
    border-radius: 1rem;
    position: relative;
    transition: all 0.3s ease;
    border: 2px solid var(--border);
}

.toggle-slider::before {
    content: '';
    position: absolute;
    width: 1rem;
    height: 1rem;
    background: var(--text-muted);
    border-radius: 50%;
    top: 50%;
    left: 0.25rem;
    transform: translateY(-50%);
    transition: all 0.3s ease;
}

.toggle-label input:checked + .toggle-slider {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
}

.toggle-label input:checked + .toggle-slider::before {
    left: 1.75rem;
    background: white;
}

/* Column Styles */
.input-column, .output-column, .third-column {
    display: flex;
    flex-direction: column;
    height: fit-content;
}

.text-section {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 1rem;
    border: 1px solid var(--border);
    box-shadow: 0 4px 12px var(--shadow);
    display: flex;
    flex-direction: column;
    gap: 1rem;
    height: 100%;
}

/* Input/Output/Third Sections */
.input-section, .output-section, .third-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    flex: 1;
    height: 100%;
}

/* Ensure input and output sections have equal heights */
.content-grid .input-section,
.content-grid .output-section {
    min-height: 350px;
}

.content-grid .input-section textarea,
.content-grid .output-section textarea {
    flex: 1;
    min-height: 280px;
    resize: vertical;
}

.input-header, .output-header, .third-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.input-header label, .output-header label, .third-header label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.input-actions, .output-actions, .third-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.action-btn {
    background: var(--bg-tertiary);
    border: 2px solid var(--border);
    border-radius: 0.5rem;
    padding: 0.5rem;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-primary);
}

.action-btn:hover {
    border-color: var(--accent-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow);
}

.char-count {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    background: var(--bg-tertiary);
    border-radius: 0.25rem;
    border: 1px solid var(--border);
}

textarea {
    background: var(--bg-tertiary);
    border: 2px solid var(--border);
    border-radius: 0.75rem;
    padding: 1rem;
    color: var(--text-primary);
    font-size: 1rem;
    line-height: 1.6;
    resize: vertical;
    min-height: 120px;
    transition: all 0.3s ease;
    font-family: 'Inter', monospace;
}

textarea:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

textarea::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

#outputText, #thirdText {
    font-family: 'Courier New', monospace;
    background: var(--bg-primary);
}

#thirdText {
    background: var(--bg-tertiary);
}

/* Key Inputs */
.key-inputs {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.primary-actions {
    display: flex;
    gap: 1rem;
    flex: 1;
    justify-self: space-between;
}

.utility-actions {
    display: flex;
    gap: 0.5rem;
}

.btn {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--accent-primary);
    color: white;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
    background: var(--accent-secondary);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.btn-secondary {
    background: var(--success);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-secondary:hover {
    background: #059669;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.btn-utility {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 2px solid var(--border);
    padding: 0.75rem;
    font-size: 1.2rem;
    min-width: auto;
}

.btn-utility:hover {
    border-color: var(--accent-primary);
    color: var(--accent-primary);
    transform: translateY(-2px);
}

/* Info Panel */
.info-panel {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid var(--border);
    box-shadow: 0 4px 12px var(--shadow);
}

.info-panel h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.algorithm-info p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.algorithm-details {
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: 0.5rem;
    border: 1px solid var(--border);
}

.algorithm-details h4 {
    color: var(--accent-primary);
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.algorithm-details p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.algorithm-details code {
    background: var(--bg-primary);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    color: var(--accent-secondary);
    border: 1px solid var(--border);
}

.security-level {
    font-weight: 600;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
}

/* Notifications */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid var(--border);
    box-shadow: 0 8px 24px var(--shadow);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1000;
    max-width: 300px;
    font-weight: 500;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-success {
    border-left: 4px solid var(--success);
}

.notification-warning {
    border-left: 4px solid var(--warning);
}

.notification-error {
    border-left: 4px solid var(--error);
}

.notification-info {
    border-left: 4px solid var(--accent-primary);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 1.5s infinite;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        min-height: auto;
    }

    .content-grid .input-section,
    .content-grid .output-section {
        min-height: 250px;
    }

    .content-grid .input-section textarea,
    .content-grid .output-section textarea {
        min-height: 180px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    h1 {
        font-size: 2rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        min-height: auto;
    }

    .content-grid .input-section,
    .content-grid .output-section {
        min-height: 200px;
    }

    .content-grid .input-section textarea,
    .content-grid .output-section textarea {
        min-height: 150px;
    }

    .compact-select {
        max-width: 100%;
    }

    .algorithm-settings-row {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .action-buttons {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .primary-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .utility-actions {
        justify-content: center;
    }

    .input-header, .output-header, .third-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .input-actions, .output-actions, .third-actions {
        width: 100%;
        justify-content: space-between;
    }

    .theme-toggle {
        position: static;
        margin: 1rem auto 0;
    }

    .notification {
        right: 1rem;
        left: 1rem;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0.5rem;
    }

    .text-section, .controls-section, .info-panel {
        padding: 1rem;
    }

    h1 {
        font-size: 1.5rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    textarea {
        min-height: 100px;
        font-size: 0.9rem;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border: #ffffff;
        --border-focus: #00ff00;
    }

    [data-theme="light"] {
        --border: #000000;
        --border-focus: #0000ff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print styles */
@media print {
    .theme-toggle,
    .action-btn,
    .btn,
    .notification {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .container {
        max-width: none;
        padding: 0;
    }

    .text-section,
    .controls-section,
    .info-panel {
        background: white !important;
        border: 1px solid black !important;
        box-shadow: none !important;
    }
}

/* Focus indicators for accessibility */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-primary);
}

/* Selection styles */
::selection {
    background: var(--accent-primary);
    color: white;
}

::-moz-selection {
    background: var(--accent-primary);
    color: white;
}
