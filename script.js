// Enhanced Encryption Application
// Supports multiple encryption algorithms with modern UI/UX

class EncryptionApp {
    constructor() {
        this.alphabetArray = 'abcdefghijklmnopqrstuvwxyz'.split('');
        this.currentAlgorithm = 'caesar';
        this.isRealTimeEnabled = false;
        this.theme = localStorage.getItem('theme') || 'dark';

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateCharacterCount();
        this.updateAlgorithmInfo();
        this.toggleKeyInput();
        this.applyTheme();
        this.showWelcomeAnimation();
    }

    setupEventListeners() {
        // Real-time encryption toggle
        const realTimeToggle = document.getElementById('realTimeToggle');
        if (realTimeToggle) {
            realTimeToggle.addEventListener('change', (e) => {
                this.isRealTimeEnabled = e.target.checked;
                if (this.isRealTimeEnabled) {
                    this.processText();
                }
            });
        }

        // Input text changes
        const inputText = document.getElementById('inputText');
        if (inputText) {
            inputText.addEventListener('input', () => {
                this.updateCharacterCount();
                if (this.isRealTimeEnabled) {
                    this.processText();
                }
            });
        }

        // Shift value changes
        const inputShift = document.getElementById('inputShift');
        if (inputShift) {
            inputShift.addEventListener('input', () => {
                if (this.isRealTimeEnabled) {
                    this.processText();
                }
            });
        }

        // Algorithm selection
        const algorithmSelect = document.getElementById('algorithmSelect');
        if (algorithmSelect) {
            algorithmSelect.addEventListener('change', (e) => {
                this.currentAlgorithm = e.target.value;
                this.toggleKeyInput();
                this.updateAlgorithmInfo();
                if (this.isRealTimeEnabled) {
                    this.processText();
                }
            });
        }

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    // Caesar Cipher Implementation
    caesarCipher(text, shift, decrypt = false) {
        if (!text) return '';

        const actualShift = decrypt ? -shift : shift;
        let result = '';

        for (let char of text) {
            if (this.alphabetArray.includes(char.toLowerCase())) {
                const isUpperCase = char === char.toUpperCase();
                const lowerChar = char.toLowerCase();
                const currentIndex = this.alphabetArray.indexOf(lowerChar);
                let newIndex = (currentIndex + actualShift) % this.alphabetArray.length;

                if (newIndex < 0) {
                    newIndex += this.alphabetArray.length;
                }

                let newChar = this.alphabetArray[newIndex];
                result += isUpperCase ? newChar.toUpperCase() : newChar;
            } else {
                result += char;
            }
        }

        return result;
    }

    // Vigenère Cipher Implementation
    vigenereCipher(text, key, decrypt = false) {
        if (!text || !key) return '';

        key = key.toLowerCase().replace(/[^a-z]/g, '');
        if (!key) return text;

        let result = '';
        let keyIndex = 0;

        for (let char of text) {
            if (this.alphabetArray.includes(char.toLowerCase())) {
                const isUpperCase = char === char.toUpperCase();
                const lowerChar = char.toLowerCase();
                const charIndex = this.alphabetArray.indexOf(lowerChar);
                const keyChar = key[keyIndex % key.length];
                const keyShift = this.alphabetArray.indexOf(keyChar);

                let newIndex;
                if (decrypt) {
                    newIndex = (charIndex - keyShift + 26) % 26;
                } else {
                    newIndex = (charIndex + keyShift) % 26;
                }

                let newChar = this.alphabetArray[newIndex];
                result += isUpperCase ? newChar.toUpperCase() : newChar;
                keyIndex++;
            } else {
                result += char;
            }
        }

        return result;
    }

    // ROT13 Implementation
    rot13(text) {
        return this.caesarCipher(text, 13);
    }



    // Atbash Cipher Implementation
    atbashCipher(text) {
        let result = '';

        for (let char of text) {
            if (this.alphabetArray.includes(char.toLowerCase())) {
                const isUpperCase = char === char.toUpperCase();
                const lowerChar = char.toLowerCase();
                const currentIndex = this.alphabetArray.indexOf(lowerChar);
                const newIndex = 25 - currentIndex;
                let newChar = this.alphabetArray[newIndex];
                result += isUpperCase ? newChar.toUpperCase() : newChar;
            } else {
                result += char;
            }
        }

        return result;
    }

    // Main processing function
    processText(isDecrypt = false) {
        const inputText = document.getElementById('inputText').value;
        const shiftAmount = parseInt(document.getElementById('inputShift').value) || 0;
        const keyInput = document.getElementById('keyInput').value || '';

        if (!inputText.trim()) {
            this.setOutput('');
            return;
        }

        let result = '';

        try {
            switch (this.currentAlgorithm) {
                case 'caesar':
                    result = this.caesarCipher(inputText, shiftAmount, isDecrypt);
                    break;
                case 'vigenere':
                    result = this.vigenereCipher(inputText, keyInput, isDecrypt);
                    break;
                case 'rot13':
                    result = this.rot13(inputText);
                    break;
                case 'base64':
                    result = isDecrypt ? this.base64Decode(inputText) : this.base64Encode(inputText);
                    break;
                case 'atbash':
                    result = this.atbashCipher(inputText);
                    break;
                default:
                    result = 'Unknown algorithm';
            }
        } catch (error) {
            result = `Error: ${error.message}`;
        }

        this.setOutput(result);
        this.animateOutput();
    }

    setOutput(text) {
        const outputElement = document.getElementById('outputText');
        if (outputElement) {
            outputElement.value = text;
            this.updateOutputCharacterCount();
        }
    }

    // Encrypt function (public interface)
    encryptData() {
        if (this.validateInput()) {
            this.processText(false);
            this.showNotification('Text encrypted successfully!', 'success');
        }
    }

    // Decrypt function (public interface)
    decryptData() {
        if (this.validateInput()) {
            this.processText(true);
            this.showNotification('Text decrypted successfully!', 'success');
        }
    }

    // Utility Functions

    clearAll() {
        document.getElementById('inputText').value = '';
        document.getElementById('outputText').value = '';
        document.getElementById('inputShift').value = '';
        document.getElementById('keyInput').value = '';
        this.updateCharacterCount();
        this.updateOutputCharacterCount();
        this.showNotification('All fields cleared!', 'info');
    }

    swapInputOutput() {
        const inputText = document.getElementById('inputText');
        const outputText = document.getElementById('outputText');

        if (inputText && outputText) {
            const temp = inputText.value;
            inputText.value = outputText.value;
            outputText.value = temp;
            this.updateCharacterCount();
            this.updateOutputCharacterCount();
            this.showNotification('Input and output swapped!', 'info');
        }
    }

    downloadAsFile() {
        const outputText = document.getElementById('outputText').value;
        if (!outputText) {
            this.showNotification('Nothing to download!', 'warning');
            return;
        }

        const blob = new Blob([outputText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `encrypted_text_${new Date().getTime()}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        this.showNotification('File downloaded!', 'success');
    }

    uploadFile() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.txt';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    document.getElementById('inputText').value = e.target.result;
                    this.updateCharacterCount();
                    this.showNotification('File uploaded successfully!', 'success');
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    // UI Enhancement Functions
    updateCharacterCount() {
        const inputText = document.getElementById('inputText');
        const charCount = document.getElementById('charCount');

        if (inputText && charCount) {
            const count = inputText.value.length;
            charCount.textContent = `${count} characters`;

            // Add visual feedback for long text
            if (count > 1000) {
                charCount.style.color = '#f85149';
            } else if (count > 500) {
                charCount.style.color = '#f0883e';
            } else {
                charCount.style.color = '#7c3aed';
            }
        }
    }

    updateOutputCharacterCount() {
        const outputText = document.getElementById('outputText');
        const outputCharCount = document.getElementById('outputCharCount');

        if (outputText && outputCharCount) {
            const count = outputText.value.length;
            outputCharCount.textContent = `${count} characters`;
        }
    }

    // Input validation function
    validateInput() {
        const inputText = document.getElementById('inputText').value;
        const keyInput = document.getElementById('keyInput');
        const shiftInput = document.getElementById('inputShift');

        // Check if input text is empty
        if (!inputText.trim()) {
            this.showNotification('Please enter some text to encrypt/decrypt!', 'warning');
            return false;
        }

        // Check algorithm-specific requirements
        switch (this.currentAlgorithm) {
            case 'caesar':
                const shiftValue = parseInt(shiftInput.value);
                if (!shiftValue || shiftValue < 1 || shiftValue > 25) {
                    this.showNotification('Please enter a valid shift value (1-25) for Caesar cipher!', 'warning');
                    return false;
                }
                break;
            case 'vigenere':
                const keyValue = keyInput.value.trim();
                if (!keyValue) {
                    this.showNotification('Please enter a key for Vigenère cipher!', 'warning');
                    return false;
                }
                if (keyValue.length < 2) {
                    this.showNotification('Key must be at least 2 characters long!', 'warning');
                    return false;
                }
                break;
        }

        return true;
    }

    toggleKeyInput() {
        const keyInputContainer = document.getElementById('keyInputContainer');
        const algorithmSelect = document.getElementById('algorithmSelect');

        if (keyInputContainer && algorithmSelect) {
            const needsKey = ['vigenere'].includes(algorithmSelect.value);
            keyInputContainer.style.display = needsKey ? 'block' : 'none';
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // Add to page
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => notification.classList.add('show'), 100);

        // Remove after delay
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    animateOutput() {
        const outputText = document.getElementById('outputText');
        if (outputText) {
            outputText.style.transform = 'scale(0.98)';
            outputText.style.transition = 'transform 0.2s ease';
            setTimeout(() => {
                outputText.style.transform = 'scale(1)';
            }, 100);
        }
    }

    showWelcomeAnimation() {
        const main = document.querySelector('main');
        if (main) {
            main.style.opacity = '0';
            main.style.transform = 'translateY(20px)';
            setTimeout(() => {
                main.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                main.style.opacity = '1';
                main.style.transform = 'translateY(0)';
            }, 100);
        }
    }

    toggleTheme() {
        this.theme = this.theme === 'dark' ? 'light' : 'dark';
        localStorage.setItem('theme', this.theme);
        this.applyTheme();
    }

    applyTheme() {
        document.body.setAttribute('data-theme', this.theme);
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.textContent = this.theme === 'dark' ? '☀️' : '🌙';
        }
    }

    updateAlgorithmInfo() {
        const algorithmInfo = document.getElementById('algorithmInfo');
        if (!algorithmInfo) return;

        const algorithmDescriptions = {
            caesar: {
                title: 'Caesar Cipher',
                description: 'A simple substitution cipher where each letter is shifted by a fixed number of positions in the alphabet. Named after Julius Caesar who used it for military communications.',
                security: 'Low - easily broken with frequency analysis',
                example: 'A → D (shift of 3)'
            },
            vigenere: {
                title: 'Vigenère Cipher',
                description: 'A polyalphabetic substitution cipher that uses a keyword to determine the shift for each letter. Much stronger than Caesar cipher.',
                security: 'Medium - resistant to frequency analysis',
                example: 'Key: "KEY" → A+K=K, B+E=F, C+Y=A'
            },
            rot13: {
                title: 'ROT13',
                description: 'A special case of Caesar cipher with a fixed shift of 13. Commonly used in online forums to hide spoilers or offensive content.',
                security: 'Very Low - fixed shift makes it trivial to decode',
                example: 'A → N, B → O, C → P'
            },
            base64: {
                title: 'Base64 Encoding',
                description: 'Not actually encryption, but encoding that converts binary data to ASCII text. Commonly used for data transmission and storage.',
                security: 'None - this is encoding, not encryption',
                example: 'Hello → SGVsbG8='
            },
            atbash: {
                title: 'Atbash Cipher',
                description: 'An ancient Hebrew cipher where each letter is replaced with its mirror letter from the alphabet (A↔Z, B↔Y, etc.).',
                security: 'Very Low - simple substitution pattern',
                example: 'A → Z, B → Y, C → X'
            }
        };

        const info = algorithmDescriptions[this.currentAlgorithm];
        if (info) {
            algorithmInfo.innerHTML = `
                <div class="algorithm-details">
                    <h4>${info.title}</h4>
                    <p><strong>Description:</strong> ${info.description}</p>
                    <p><strong>Security Level:</strong> <span class="security-level">${info.security}</span></p>
                    <p><strong>Example:</strong> <code>${info.example}</code></p>
                </div>
            `;
        }
    }

    // Enhanced Base64 functions to avoid deprecated methods
    base64Encode(text) {
        try {
            // Use TextEncoder for modern browsers
            if (typeof TextEncoder !== 'undefined') {
                const encoder = new TextEncoder();
                const data = encoder.encode(text);
                return btoa(String.fromCharCode(...data));
            }
            // Fallback for older browsers
            return btoa(encodeURIComponent(text).replace(/%([0-9A-F]{2})/g, (_, p1) => {
                return String.fromCharCode(parseInt(p1, 16));
            }));
        } catch (e) {
            return 'Error: Invalid characters for Base64 encoding';
        }
    }

    base64Decode(text) {
        try {
            // Use TextDecoder for modern browsers
            if (typeof TextDecoder !== 'undefined') {
                const decoder = new TextDecoder();
                const data = atob(text);
                const bytes = new Uint8Array(data.length);
                for (let i = 0; i < data.length; i++) {
                    bytes[i] = data.charCodeAt(i);
                }
                return decoder.decode(bytes);
            }
            // Fallback for older browsers
            const decoded = atob(text);
            return decodeURIComponent(decoded.split('').map(c => {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }).join(''));
        } catch (e) {
            return 'Error: Invalid Base64 string';
        }
    }

    // Enhanced copy function with modern clipboard API
    async copyToClipboard() {
        const outputText = document.getElementById('outputText');
        if (!outputText || !outputText.value) {
            this.showNotification('Nothing to copy!', 'warning');
            return;
        }

        try {
            // Try modern clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(outputText.value);
                this.showNotification('Copied to clipboard!', 'success');
            } else {
                // Fallback for older browsers or non-secure contexts
                outputText.select();
                outputText.setSelectionRange(0, 99999); // For mobile devices
                const successful = document.execCommand('copy');
                if (successful) {
                    this.showNotification('Copied to clipboard!', 'success');
                } else {
                    throw new Error('Copy command failed');
                }
            }
        } catch (err) {
            this.showNotification('Failed to copy to clipboard', 'error');
            console.error('Copy failed:', err);
        }
    }
}

// Initialize the application
let encryptionApp;

// Legacy function support for existing HTML
function encryptData() {
    if (encryptionApp) {
        encryptionApp.encryptData();
    }
}

function decryptData() {
    if (encryptionApp) {
        encryptionApp.decryptData();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    encryptionApp = new EncryptionApp();
});
